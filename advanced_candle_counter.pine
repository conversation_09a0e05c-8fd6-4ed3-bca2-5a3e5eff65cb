// © ZenLuo
// @version=6

indicator("高级K线计数器 (可自定义)", "高级计数器", overlay=true, max_labels_count=500)

// 用户输入参数
display_mode = input.string("每隔N根K线", "显示模式", options=["每隔N根K线", "每隔N分钟", "显示所有数字"])
label_interval = input.int(5, title="间隔数量 (K线数或分钟数)", minval=1)
highlight_k_num = input.int(12, title="高亮显示的K线编号", minval=1)
color_default = input.color(color.new(color.gray, 50), "默认颜色")
color_highlight = input.color(color.new(color.orange, 0), "高亮颜色")
special_num1 = input.int(5, "特殊标签1", minval=1)
special_color1 = input.color(color.new(color.blue, 0), "特殊标签1颜色")
special_num2 = input.int(10, "特殊标签2", minval=1)
special_color2 = input.color(color.new(color.red, 0), "特殊标签2颜色")
text_size = input.string(size.small, "文字大小", options=[size.tiny, size.small, size.normal, size.large, size.huge])
show_reference_line = input.bool(true, "显示第130根K线参考线")
reference_line_number = input.int(130, "参考线K线编号", minval=1)

// 颜色选择逻辑 - 支持多个特殊标签颜色（必须在使用前定义）
getColorForCount(count) =>
    if count == special_num1
        special_color1
    else if count == special_num2
        special_color2
    else if count == highlight_k_num
        color_highlight
    else
        color_default

// K线计数逻辑 - 从当前位置往回数，适应不同时间框架
// 计算从最新K线往回数的编号
k_count = 0

// 在最后一根K线上为所有历史K线分配编号
if barstate.islast
    // 限制处理范围，避免性能问题
    max_bars = math.min(bar_index + 1, 500)

    // 为每根K线分配从1开始的编号（最新的是1，往前依次是2,3,4...）
    for i = 0 to max_bars - 1
        if bar_index - i >= 0
            current_bar_index = bar_index - i
            bars_back = i + 1  // 从1开始计数

            // 检查显示条件
            show_this_bar = false

            if display_mode == "显示所有数字"
                show_this_bar := true
            else if display_mode == "每隔N根K线"
                show_this_bar := (bars_back % label_interval == 0)
            else if display_mode == "每隔N分钟"
                // 对于时间模式，检查该K线的时间
                bar_minute = minute[i]
                show_this_bar := (bar_minute % label_interval == 0)

            // 显示标签
            if show_this_bar
                label.new(current_bar_index, high[i], str.tostring(bars_back),
                     color=color.new(color.white, 100),
                     textcolor=getColorForCount(bars_back),
                     style=label.style_none,
                     size=text_size,
                     yloc=yloc.abovebar)

            // 绘制参考线
            if show_reference_line and bars_back == reference_line_number
                line.new(current_bar_index, low[i], current_bar_index, high[i],
                     color=color.new(color.purple, 0),
                     width=3,
                     style=line.style_solid,
                     extend=extend.both)

                label.new(current_bar_index, high[i], "第" + str.tostring(reference_line_number) + "根",
                     color=color.new(color.purple, 10),
                     textcolor=color.white,
                     style=label.style_label_down,
                     size=size.large)

// 设置当前K线编号用于调试显示
current_k_count = barstate.islast ? 1 : 0

// 显示条件逻辑 - 根据用户选择的显示模式
show_text = false

if display_mode == "显示所有数字"
    show_text := true
else if display_mode == "每隔N根K线"
    show_text := (k_count % label_interval == 0)
else if display_mode == "每隔N分钟"
    // 计算当前时间的分钟数
    current_minute = minute
    // 检查是否是指定间隔的分钟（例如每5分钟：0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55）
    show_text := (current_minute % label_interval == 0)

// 在每根K线上方显示标签（如果满足显示条件）
if show_text
    label.new(bar_index, high, str.tostring(k_count),
         color=color.new(color.white, 100),  // 完全透明背景
         textcolor=getColorForCount(k_count),
         style=label.style_none,  // 无背景样式
         size=text_size,
         yloc=yloc.abovebar)  // 固定在K线上方

// 绘制参考线 - 标记第130根K线位置
if show_reference_line and k_count == reference_line_number
    // 绘制垂直参考线
    line.new(bar_index, low, bar_index, high,
         color=color.new(color.purple, 0),
         width=3,
         style=line.style_solid,
         extend=extend.both)

    // 在参考线上添加标签
    label.new(bar_index, high, "第" + str.tostring(reference_line_number) + "根",
         color=color.new(color.purple, 10),
         textcolor=color.white,
         style=label.style_label_down,
         size=size.large)



// 在最新K线上显示调试信息
if barstate.islast and show_reference_line
    // 获取当前时间
    current_hour = hour
    current_minute = minute
    time_str = str.tostring(current_hour) + ":" + (current_minute < 10 ? "0" + str.tostring(current_minute) : str.tostring(current_minute))

    label.new(bar_index, low, "当前K线编号:" + str.tostring(k_count) + "\n时间:" + time_str + "\n设置间隔:" + str.tostring(label_interval),
         color=color.new(color.blue, 20),
         textcolor=color.white,
         style=label.style_label_up,
         size=size.small)
