// © ZenLuo - 外汇版本
// @version=6

indicator("外汇K线计数器 (24小时交易)", "外汇计数器", overlay=true, max_labels_count=500)

// 用户输入参数
display_mode = input.string("每隔N根K线", "显示模式", options=["每隔N根K线", "每隔N分钟", "显示所有数字"])
label_interval = input.int(5, title="间隔数量 (K线数或分钟数)", minval=1)
highlight_k_num = input.int(12, title="高亮显示的K线编号", minval=1)
color_default = input.color(color.new(color.gray, 50), "默认颜色")
color_highlight = input.color(color.new(color.orange, 0), "高亮颜色")
special_num1 = input.int(5, "特殊标签1", minval=1)
special_color1 = input.color(color.new(color.blue, 0), "特殊标签1颜色")
special_num2 = input.int(10, "特殊标签2", minval=1)
special_color2 = input.color(color.new(color.red, 0), "特殊标签2颜色")
text_size = input.string(size.small, "文字大小", options=[size.tiny, size.small, size.normal, size.large, size.huge])
show_reference_line = input.bool(true, "显示第130根K线参考线")
reference_line_number = input.int(130, "参考线K线编号", minval=1)
reset_time = input.int(17, "交易日重置时间 (纽约时间)", minval=0, maxval=23)

// 颜色选择逻辑
getColorForCount(count) =>
    if count == special_num1
        special_color1
    else if count == special_num2
        special_color2
    else if count == highlight_k_num
        color_highlight
    else
        color_default

// 外汇K线计数逻辑 - 24小时交易，以纽约收盘为界
var int k_count = 0
var int last_forex_day = 0

// 获取当前时间信息
current_hour = hour
current_day = year * 10000 + month * 100 + dayofmonth
current_weekday = dayofweek

// 计算外汇交易日（以纽约时间17:00为界）
forex_day = current_hour >= reset_time ? current_day + 1 : current_day

// 检测是否为周末休市时间
// 周五17:00到周日17:00为休市时间
is_friday_close = (current_weekday == dayofweek.friday and current_hour >= reset_time)
is_weekend = (current_weekday == dayofweek.saturday) or 
             (current_weekday == dayofweek.sunday and current_hour < reset_time)
is_market_closed = is_friday_close or is_weekend

// 检测新的外汇交易日，重置计数器
if forex_day != last_forex_day and not is_market_closed
    k_count := 1
    last_forex_day := forex_day
else if not is_market_closed  // 只在交易时间内计数
    k_count += 1

// 显示条件逻辑
show_text = false

if display_mode == "显示所有数字"
    show_text := true
else if display_mode == "每隔N根K线"
    show_text := (k_count % label_interval == 0)
else if display_mode == "每隔N分钟"
    current_minute = minute
    show_text := (current_minute % label_interval == 0)

// 显示K线编号标签
if show_text and not is_market_closed
    label.new(bar_index, high, str.tostring(k_count),
         color=color.new(color.white, 100),
         textcolor=getColorForCount(k_count),
         style=label.style_none,
         size=text_size,
         yloc=yloc.abovebar)

// 绘制参考线
if show_reference_line and k_count == reference_line_number and not is_market_closed
    line.new(bar_index, low, bar_index, high,
         color=color.new(color.purple, 0),
         width=3,
         style=line.style_solid,
         extend=extend.both)

    label.new(bar_index, high, "第" + str.tostring(reference_line_number) + "根",
         color=color.new(color.purple, 10),
         textcolor=color.white,
         style=label.style_label_down,
         size=size.large)

// 显示调试信息和交易时段
if barstate.islast and show_reference_line
    time_str = str.tostring(current_hour) + ":" + (minute < 10 ? "0" + str.tostring(minute) : str.tostring(minute))
    
    // 判断当前交易时段
    session_info = ""
    if current_hour >= 17 or current_hour < 3
        session_info := "亚洲时段"
    else if current_hour >= 3 and current_hour < 12
        session_info := "欧洲时段"
    else if current_hour >= 8 and current_hour < 17
        session_info := "美洲时段"
    
    market_status = is_market_closed ? "休市" : "交易中"
    
    label.new(bar_index, low, "外汇K线编号:" + str.tostring(k_count) + 
         "\n纽约时间:" + time_str + 
         "\n市场状态:" + market_status + 
         "\n交易时段:" + session_info + 
         "\n重置时间:" + str.tostring(reset_time) + ":00" +
         "\n设置间隔:" + str.tostring(label_interval),
         color=color.new(color.blue, 20),
         textcolor=color.white,
         style=label.style_label_up,
         size=size.small)
